import { defineStore } from 'pinia';
import { statsApi } from '@/api';

/**
 * 统计信息状态管理
 * 管理系统统计数据相关的状态和操作
 */
export const useStatsStore = defineStore('stats', {
  state: () => ({
    kb_count: 0,
    total_documents: 0,
    total_chunks: 0,
    current_kb: '',
    loading: false,
    error: null
  }),
  
  actions: {
    /**
     * 获取统计信息
     * 从后端获取系统统计数据并更新本地状态
     * @returns {Object} 统计数据
     */
    async fetchStats() {
      this.loading = true;
      
      try {
        const response = await statsApi.getStats();
        console.log('统计API响应:', response);
        
        // 检查响应结构，处理嵌套的data字段
        const statsData = response.data && response.data.status === 'success' && response.data.data
          ? response.data.data  // 处理 {status: 'success', data: {...}} 结构
          : response.data;      // 兼容直接返回数据的情况
        
        console.log('处理后的统计数据:', statsData);
        
        // 更新状态
        this.kb_count = statsData.kb_count || 0;
        this.total_documents = statsData.total_documents || 0;
        this.total_chunks = statsData.total_chunks || 0;
        this.current_kb = statsData.current_kb || '';
        
        return statsData;
      } catch (error) {
        this.error = error.message || '获取统计信息失败';
        return {
          kb_count: 0,
          total_documents: 0,
          total_chunks: 0,
          current_kb: ''
        };
      } finally {
        this.loading = false;
      }
    }
  }
});
