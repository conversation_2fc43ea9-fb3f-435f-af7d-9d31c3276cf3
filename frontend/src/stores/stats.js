import { defineStore } from 'pinia';
import { statsApi } from '@/api';

/**
 * 统计信息状态管理
 * 管理系统统计数据相关的状态和操作
 */
export const useStatsStore = defineStore('stats', {
  state: () => ({
    kb_count: 0,
    total_documents: 0,
    total_chunks: 0,
    current_kb: '',
    loading: false,
    error: null
  }),
  
  actions: {
    /**
     * 获取统计信息
     * 从后端获取系统统计数据并更新本地状态
     * @returns {Object} 统计数据
     */
    async fetchStats() {
      this.loading = true;
      
      try {
        const response = await statsApi.get();
        
        this.kb_count = response.data.kb_count || 0;
        this.total_documents = response.data.total_documents || 0;
        this.total_chunks = response.data.total_chunks || 0;
        this.current_kb = response.data.current_kb || '';
        
        return response.data;
      } catch (error) {
        this.error = error.message || '获取统计信息失败';
        return {
          kb_count: 0,
          total_documents: 0,
          total_chunks: 0,
          current_kb: ''
        };
      } finally {
        this.loading = false;
      }
    }
  }
});
