import { defineStore } from 'pinia';
import { queryApi } from '@/api';
import { useKnowledgeBaseStore } from './knowledgeBase';

/**
 * 查询状态管理
 * 管理问答查询相关的状态和操作
 */
export const useQueryStore = defineStore('query', {
  state: () => ({
    question: '',
    answer: '',
    sources: [],
    loading: false,
    error: null
  }),
  
  actions: {
    /**
     * 提交查询问题
     * 向后端发送问题并获取回答结果
     * @param {string} question - 用户输入的问题
     * @returns {Object} 查询结果，包含答案和来源文档
     */
    async submitQuery(question) {
      this.loading = true;
      this.question = question;
      
      try {
        const kbStore = useKnowledgeBaseStore();
        const currentKB = kbStore.currentKnowledgeBase?.name || '';
        
        const response = await queryApi.submit(question, currentKB);
        
        this.answer = response.data.answer || '';
        this.sources = response.data.sources || [];
        this.error = response.data.error || null;
        
        return response.data;
      } catch (error) {
        this.error = error.message || '查询失败';
        this.answer = '';
        this.sources = [];
        
        return {
          answer: '',
          sources: [],
          error: this.error
        };
      } finally {
        this.loading = false;
      }
    }
  }
});
