import { defineStore } from 'pinia';
import { queryApi } from '@/api';
import { useKnowledgeBaseStore } from './knowledgeBase';

/**
 * 查询状态管理
 * 管理问答查询相关的状态和操作
 */
export const useQueryStore = defineStore('query', {
  state: () => ({
    question: '',
    answer: '',
    sources: [],
    loading: false,
    error: null
  }),

  actions: {
    /**
     * 提交查询问题
     * 向后端发送问题并获取回答结果
     * @param {string} question - 用户输入的问题
     * @returns {Object} 查询结果，包含答案和来源文档
     */
    async submitQuery(question) {
      this.loading = true;
      this.question = question;
      this.error = null;

      try {
        const kbStore = useKnowledgeBaseStore();
        
        // 获取当前选中的知识库
        let currentKB = '';
        
        // 先从知识库列表中查找被标记为当前知识库的项
        const currentKbFromList = kbStore.knowledgeBases.find(kb => kb.is_current);
        if (currentKbFromList) {
          currentKB = currentKbFromList.name;
          console.log('从知识库列表找到当前知识库:', currentKB);
        } 
        // 如果没找到，尝试使用 currentKnowledgeBase 值
        else if (kbStore.currentKnowledgeBase) {
          currentKB = kbStore.currentKnowledgeBase;
          console.log('使用 store 中的当前知识库:', currentKB);
        }
        
        console.log('提交问题到知识库:', currentKB || '默认知识库');

        const response = await queryApi.submit(question, currentKB);

        // 检查响应中是否包含错误信息
        if (response.data.error) {
          this.error = response.data.error;
          console.error('查询返回错误:', this.error);
        }

        this.answer = response.data.answer || '';
        this.sources = response.data.sources || [];

        return response.data;
      } catch (error) {
        // 处理网络错误或其他异常
        console.error('查询请求失败:', error);

        const errorMessage = error.response?.data?.message ||
                            error.response?.data?.error ||
                            error.message ||
                            '查询失败，请检查网络连接';

        this.error = errorMessage;
        this.answer = '查询过程中出错，请重试';
        this.sources = [];

        return {
          answer: this.answer,
          sources: [],
          error: this.error
        };
      } finally {
        this.loading = false;
      }
    }
  }
});
