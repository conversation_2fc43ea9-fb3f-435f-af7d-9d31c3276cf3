import { defineStore } from 'pinia';
import { ref } from 'vue';
import { knowledgeBaseApi } from '@/api/knowledgeBase';

export const useKnowledgeBaseStore = defineStore('knowledgeBase', () => {
  // 状态
  const knowledgeBases = ref([]);
  const currentKnowledgeBase = ref(null);
  const loading = ref(false);
  const error = ref(null);

  /**
   * 获取所有知识库列表
   */
  async function fetchKnowledgeBases() {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await knowledgeBaseApi.getAll();
      console.log('知识库列表响应:', response);
      knowledgeBases.value = Array.isArray(response.data) ? response.data : [];
      
      // 设置当前知识库
      const current = knowledgeBases.value.find(kb => kb.is_current);
      if (current) {
        currentKnowledgeBase.value = current.name;
      } else if (knowledgeBases.value.length > 0 && !currentKnowledgeBase.value) {
        currentKnowledgeBase.value = knowledgeBases.value[0].name;
      }
    } catch (err) {
      error.value = err.message;
      console.error('获取知识库列表失败:', err);
    } finally {
      loading.value = false;
    }
  }

  /**
   * 设置当前选中的知识库
   * @param {string} name - 知识库名称
   * @returns {Promise<Object>} 设置结果
   */
  async function setCurrentKnowledgeBase(name) {
    loading.value = true;
    error.value = null;

    try {
      const response = await knowledgeBaseApi.setCurrent(name);
      if (response.data.status === 'success') {
        currentKnowledgeBase.value = name;
        await fetchKnowledgeBases(); // 刷新知识库列表
      }
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.message || err.message || '切换知识库失败';
      error.value = errorMessage;
      console.error('切换知识库失败:', err);
      return { status: 'error', message: errorMessage };
    } finally {
      loading.value = false;
    }
  }

  /**
   * 创建新知识库
   * @param {string} name - 知识库名称
   * @returns {Promise<Object>} 创建结果
   */
  async function createKnowledgeBase(name) {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await knowledgeBaseApi.create(name);
      if (response.data.status === 'success') {
        await fetchKnowledgeBases(); // 刷新知识库列表
      }
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.message || err.message || '创建知识库失败';
      error.value = errorMessage;
      console.error('创建知识库失败:', err);
      return { status: 'error', message: errorMessage };
    } finally {
      loading.value = false;
    }
  }

  return {
    knowledgeBases,
    currentKnowledgeBase,
    loading,
    error,
    fetchKnowledgeBases,
    setCurrentKnowledgeBase,
    createKnowledgeBase,
  };
});
