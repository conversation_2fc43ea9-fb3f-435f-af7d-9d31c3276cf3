import { defineStore } from 'pinia';
import { documentApi } from '@/api';

/**
 * 文档管理状态
 * 管理文档上传相关的状态和操作
 */
export const useDocumentStore = defineStore('document', {
  state: () => ({
    loading: false,
    error: null
  }),
  
  actions: {
    /**
     * 上传文档
     * 将用户选择的文档上传到指定知识库
     * @param {FormData} formData - 包含文件和知识库名称的表单数据
     * @returns {Object} 上传结果
     */
    async uploadDocuments(formData) {
      this.loading = true;
      
      try {
        const response = await documentApi.upload(formData);
        return response.data;
      } catch (error) {
        this.error = error.message || '上传文档失败';
        return { status: 'error', message: this.error };
      } finally {
        this.loading = false;
      }
    }
  }
});
