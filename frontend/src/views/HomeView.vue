<template>
  <div class="home-container">
    <el-container>
      <!-- 头部导航栏 -->
      <el-header>
        <div class="header-content">
          <div class="logo-area">
            <h1 class="app-title">海外业务知识库问答系统</h1>
          </div>
          <div class="action-buttons">
            <el-button type="primary" @click="showCreateKBModal = true">
              <el-icon><Plus /></el-icon>
              <span>新建知识库</span>
            </el-button>
            <el-button type="success" @click="showUploadModal = true">
              <el-icon><Upload /></el-icon>
              <span>上传文档</span>
            </el-button>
          </div>
        </div>
      </el-header>
      
      <!-- 主要内容区域 -->
      <el-main>
        <!-- 统计信息面板 -->
        <StatsPanel />
        
        <!-- 知识库选择区域 -->
        <el-card class="knowledge-base-card" shadow="hover">
          <template #header>
            <div class="kb-header">
              <h3>选择知识库</h3>
            </div>
          </template>
          <KnowledgeBaseSelector 
            v-model="selectedKnowledgeBase" 
            @change="onKnowledgeBaseChange" 
          />
        </el-card>
        
        <!-- 聊天/问答面板 -->
        <ChatPanel />
      </el-main>
      
      <!-- 页脚区域 -->
      <el-footer>
        <div class="footer-content">
          <span>海外业务知识库问答系统 &copy; {{ new Date().getFullYear() }}</span>
          <span>
            基于 <el-link href="#" type="primary">LangChain v0.3</el-link> 和 
            <el-link href="#" type="primary">DeepSeek V3</el-link> 构建
          </span>
        </div>
      </el-footer>
    </el-container>
    
    <!-- 创建知识库模态框 -->
    <CreateKnowledgeBaseModal 
      :is-open="showCreateKBModal" 
      @close="showCreateKBModal = false"
      @created="onKnowledgeBaseCreated"
    />
    
    <!-- 上传文档模态框 -->
    <UploadDocumentModal 
      :is-open="showUploadModal" 
      @close="showUploadModal = false"
      @uploaded="onDocumentUploaded"
    />
  </div>
</template>

<script setup>
/**
 * 主页视图组件
 * 整合所有功能模块，实现完整的应用界面
 */
import { ref } from 'vue';
import { Plus, Upload } from '@element-plus/icons-vue';
import StatsPanel from '@/components/stats/StatsPanel.vue';
import KnowledgeBaseSelector from '@/components/knowledge-base/KnowledgeBaseSelector.vue';
import ChatPanel from '@/components/chat/ChatPanel.vue';
import CreateKnowledgeBaseModal from '@/components/knowledge-base/CreateKnowledgeBaseModal.vue';
import UploadDocumentModal from '@/components/document/UploadDocumentModal.vue';
import { useKnowledgeBaseStore } from '@/stores/knowledgeBase';

/**
 * 知识库存储实例
 * 用于管理知识库相关操作
 */
const kbStore = useKnowledgeBaseStore();

/**
 * 组件状态
 */
const selectedKnowledgeBase = ref(null);
const showCreateKBModal = ref(false);
const showUploadModal = ref(false);

/**
 * 处理知识库变更
 * 当用户选择不同知识库时触发
 * @param {Object} kb - 选中的知识库对象
 */
const onKnowledgeBaseChange = (kb) => {
  if (kb) {
    kbStore.setCurrentKnowledgeBase(kb.name);
    // 触发知识库变更事件，更新统计数据等
    window.dispatchEvent(new CustomEvent('knowledgeBaseChanged'));
  }
};

/**
 * 处理知识库创建成功
 * 当成功创建新知识库后，刷新列表并选中新创建的知识库
 * @param {Object} newKB - 新创建的知识库信息
 */
const onKnowledgeBaseCreated = async (newKB) => {
  // 刷新知识库列表
  await kbStore.fetchKnowledgeBases();
  
  // 选择新创建的知识库
  const kb = kbStore.knowledgeBases.find(k => k.name === newKB.name);
  if (kb) {
    selectedKnowledgeBase.value = kb;
    onKnowledgeBaseChange(kb);
  }
};

/**
 * 处理文档上传成功
 * 当成功上传文档后，触发更新事件刷新统计数据
 */
const onDocumentUploaded = () => {
  // 触发知识库变更事件，刷新统计数据
  window.dispatchEvent(new CustomEvent('knowledgeBaseChanged'));
};
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.el-container {
  min-height: 100vh;
}

.el-header {
  background-color: var(--el-color-primary);
  color: white;
  padding: 0 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.app-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.el-main {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.knowledge-base-card {
  margin-bottom: 20px;
}

.kb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.kb-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.el-footer {
  padding: 20px;
  background-color: var(--el-fill-color-lighter);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    gap: 5px;
  }
  
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    padding: 10px 0;
    gap: 10px;
  }
  
  .el-header {
    height: auto;
    padding: 10px 20px;
  }
  
  .footer-content {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
}
</style>
