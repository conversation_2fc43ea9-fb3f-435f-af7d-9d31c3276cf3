<template>
  <div>
    <el-select
      v-model="selectedKnowledgeBase"
      class="w-100"
      placeholder="选择知识库"
      @change="onChange"
      :loading="kbStore.loading"
    >
      <el-option
        v-for="kb in knowledgeBases"
        :key="kb.name"
        :label="`${kb.name} (文档数: ${kb.document_count})`"
        :value="kb"
      >
        <div class="flex-between">
          <span>{{ kb.name }}</span>
          <span class="text-info">文档数: {{ kb.document_count }}</span>
        </div>
      </el-option>
    </el-select>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { useKnowledgeBaseStore } from '@/stores/knowledgeBase';

const props = defineProps({
  modelValue: Object,
});

const emit = defineEmits(['update:modelValue', 'change']);

/**
 * 知识库存储实例
 * 用于获取和管理知识库数据
 */
const kbStore = useKnowledgeBaseStore();

/**
 * 知识库列表
 * 包含所有可用的知识库
 */
const knowledgeBases = ref([]);

/**
 * 当前选中的知识库
 * 通过v-model与父组件双向绑定
 */
const selectedKnowledgeBase = ref(props.modelValue || null);

/**
 * 加载知识库列表
 * 从后端获取知识库数据并处理选中状态
 */
const loadKnowledgeBases = async () => {
  knowledgeBases.value = await kbStore.fetchKnowledgeBases();
  // 如果没有选中的知识库，则选择第一个
  if (!selectedKnowledgeBase.value && knowledgeBases.value.length > 0) {
    const current = knowledgeBases.value.find(kb => kb.is_current) || knowledgeBases.value[0];
    selectedKnowledgeBase.value = current;
    emit('update:modelValue', current);
  }
};

/**
 * 处理知识库选择变化
 * 当用户选择新的知识库时，更新状态并通知父组件
 * @param {Object} kb - 选中的知识库对象
 */
const onChange = (kb) => {
  emit('update:modelValue', kb);
  emit('change', kb);
  kbStore.setCurrentKnowledgeBase(kb.name);
};

// 监听外部属性变化
watch(() => props.modelValue, (val) => {
  selectedKnowledgeBase.value = val;
});

// 页面加载时获取知识库列表
onMounted(() => {
  loadKnowledgeBases();
});
</script>

<style scoped>
.w-100 {
  width: 100%;
}
</style>
