<template>
  <div>
    <el-select
      v-model="selectedKnowledgeBase"
      class="w-100"
      placeholder="选择知识库"
      @change="onChange"
      @visible-change="onDropdownVisibleChange"
      :loading="kbStore.loading"
      value-key="id"
      :popper-append-to-body="true"
      popper-class="kb-selector-dropdown"
    >
      <el-option
        v-for="kb in knowledgeBases"
        :key="kb.id || kb.name"
        :label="`${kb.name} (文档数: ${kb.document_count})`"
        :value="kb"
      >
        <div class="flex-between">
          <span>{{ kb.name }}</span>
          <span class="text-info">文档数: {{ kb.document_count }}</span>
        </div>
      </el-option>
    </el-select>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { useKnowledgeBaseStore } from '@/stores/knowledgeBase';

const props = defineProps({
  modelValue: Object,
});

const emit = defineEmits(['update:modelValue', 'change']);

/**
 * 知识库存储实例
 * 用于获取和管理知识库数据
 */
const kbStore = useKnowledgeBaseStore();

/**
 * 知识库列表
 * 包含所有可用的知识库
 */
const knowledgeBases = ref([]);

/**
 * 当前选中的知识库
 * 通过v-model与父组件双向绑定
 */
const selectedKnowledgeBase = ref(null);

// 初始化时设置选中值
if (props.modelValue) {
  console.log('初始化知识库选择器，传入值:', props.modelValue);
  selectedKnowledgeBase.value = props.modelValue;
}

/**
 * 查找知识库对象
 * 根据知识库名称查找完整的知识库对象
 * @param {string} name - 知识库名称
 * @returns {Object|null} - 找到的知识库对象或null
 */
const findKnowledgeBaseByName = (name) => {
  if (!name) return null;
  const result = knowledgeBases.value.find(kb => kb.name === name);
  console.log(`查找知识库 "${name}": ${result ? '找到' : '未找到'}`, result);
  return result;
};

/**
 * 为知识库对象生成唯一ID
 * 确保每个知识库对象都有唯一标识符
 * @param {Array} kbList - 知识库列表
 * @returns {Array} - 添加了id属性的知识库列表
 */
const ensureKnowledgeBaseIds = (kbList) => {
  return kbList.map((kb, index) => {
    if (!kb.id) {
      // 如果没有id，使用name作为id，或者生成一个唯一id
      kb.id = kb.name || `kb-${index}`;
    }
    return kb;
  });
};

/**
 * 加载知识库列表
 * 从后端获取知识库数据并处理选中状态
 */
const loadKnowledgeBases = async () => {
  try {
    console.log('开始加载知识库列表');
    await kbStore.fetchKnowledgeBases();
    
    // 确保每个知识库对象都有唯一ID
    const kbListWithIds = ensureKnowledgeBaseIds(kbStore.knowledgeBases);
    knowledgeBases.value = kbListWithIds;
    console.log('知识库列表加载完成:', knowledgeBases.value);

    // 如果已经有选中的知识库，确保它与列表中的对象匹配
    if (selectedKnowledgeBase.value && selectedKnowledgeBase.value.name) {
      console.log('当前已选中知识库:', selectedKnowledgeBase.value.name);
      const foundKb = findKnowledgeBaseByName(selectedKnowledgeBase.value.name);
      if (foundKb) {
        console.log('找到匹配的知识库对象:', foundKb);
        // 使用深拷贝避免引用问题
        selectedKnowledgeBase.value = JSON.parse(JSON.stringify(foundKb));
        emit('update:modelValue', selectedKnowledgeBase.value);
      } else {
        console.log('未找到匹配的知识库对象，保持当前选择');
      }
    }
    // 如果没有选中的知识库，则优先选择default知识库，其次是当前知识库，最后是第一个知识库
    else {
      console.log('没有选中的知识库，尝试选择default');
      // 优先查找名为"default"的知识库
      const defaultKb = findKnowledgeBaseByName("default");
      console.log('default知识库:', defaultKb);
      // 其次查找标记为当前的知识库
      const currentKb = knowledgeBases.value.find(kb => kb.is_current);
      console.log('当前知识库:', currentKb);
      // 最后使用第一个知识库
      const fallbackKb = knowledgeBases.value.length > 0 ? knowledgeBases.value[0] : null;
      console.log('第一个知识库:', fallbackKb);
      
      // 按优先级选择知识库
      const selectedKb = defaultKb || currentKb || fallbackKb;
      console.log('最终选择的知识库:', selectedKb);
      if (selectedKb) {
        // 使用深拷贝避免引用问题
        selectedKnowledgeBase.value = JSON.parse(JSON.stringify(selectedKb));
        emit('update:modelValue', selectedKnowledgeBase.value);
      }
    }
    
    // 强制触发一次更新
    setTimeout(() => {
      if (selectedKnowledgeBase.value) {
        const temp = selectedKnowledgeBase.value;
        selectedKnowledgeBase.value = null;
        setTimeout(() => {
          selectedKnowledgeBase.value = temp;
        }, 0);
      }
    }, 100);
    
  } catch (error) {
    console.error('加载知识库列表失败:', error);
    // 可以在这里添加错误提示，例如使用 Element Plus 的 Message 组件
    // ElMessage.error('加载知识库列表失败，请刷新页面重试');
  }
};

/**
 * 处理知识库选择变化
 * 当用户选择新的知识库时，更新状态并通知父组件
 * @param {Object} kb - 选中的知识库对象
 */
const onChange = async (kb) => {
  console.log('选择知识库变化:', kb);
  
  if (!kb) {
    console.log('选择为空，不进行操作');
    return;
  }
  
  // 确保选中的是完整对象
  const completeKb = kb.name ? kb : findKnowledgeBaseByName(kb);
  console.log('完整的知识库对象:', completeKb);
  
  if (completeKb) {
    // 使用深拷贝避免引用问题
    selectedKnowledgeBase.value = JSON.parse(JSON.stringify(completeKb));
    emit('update:modelValue', selectedKnowledgeBase.value);
    emit('change', selectedKnowledgeBase.value);

    try {
      await kbStore.setCurrentKnowledgeBase(completeKb.name);
    } catch (error) {
      console.error('切换知识库失败:', error);
      // 可以在这里添加错误提示
      // ElMessage.error(`切换知识库失败: ${error.message}`);
    }
  } else {
    console.warn('未找到完整的知识库对象，无法切换');
  }
};

/**
 * 处理下拉框显示状态变化
 * 当下拉框显示时，确保知识库列表已加载
 * @param {boolean} visible - 下拉框是否可见
 */
const onDropdownVisibleChange = (visible) => {
  console.log('下拉框显示状态变化:', visible);
  if (visible) {
    // 当下拉框显示时，确保知识库列表已加载
    loadKnowledgeBases();
    
    // 如果下拉框打开但没有选中项，尝试设置默认选项
    if (!selectedKnowledgeBase.value && knowledgeBases.value.length > 0) {
      console.log('下拉框打开时没有选中项，尝试设置默认选项');
      const defaultKb = findKnowledgeBaseByName("default") || knowledgeBases.value[0];
      if (defaultKb) {
        selectedKnowledgeBase.value = JSON.parse(JSON.stringify(defaultKb));
        emit('update:modelValue', selectedKnowledgeBase.value);
      }
    }
  }
};

// 监听外部属性变化
watch(() => props.modelValue, (val) => {
  console.log('外部modelValue变化:', val);
  if (!val) {
    console.log('清空选中值');
    selectedKnowledgeBase.value = null;
    return;
  }
  
  // 如果传入的是对象且有name属性
  if (typeof val === 'object' && val.name) {
    console.log('传入了对象:', val);
    // 查找完整的知识库对象
    const foundKb = findKnowledgeBaseByName(val.name);
    // 如果找到了匹配的知识库对象，使用它，否则使用传入的值
    if (foundKb) {
      console.log('找到匹配的知识库对象，使用它');
      selectedKnowledgeBase.value = JSON.parse(JSON.stringify(foundKb));
    } else {
      console.log('未找到匹配的知识库对象，使用传入值');
      // 确保有ID属性
      if (!val.id) val.id = val.name;
      selectedKnowledgeBase.value = val;
    }
  } else if (typeof val === 'string') {
    console.log('传入了字符串:', val);
    const foundKb = findKnowledgeBaseByName(val);
    if (foundKb) {
      selectedKnowledgeBase.value = JSON.parse(JSON.stringify(foundKb));
    }
  } else {
    console.log('传入了其他类型的值，直接使用');
    selectedKnowledgeBase.value = val;
  }
});

// 监听知识库列表变化，确保选中状态正确
watch(() => knowledgeBases.value, (newKbs) => {
  console.log('知识库列表变化，当前列表长度:', newKbs.length);
  if (selectedKnowledgeBase.value && selectedKnowledgeBase.value.name) {
    console.log('当前选中的知识库:', selectedKnowledgeBase.value.name);
    // 在新列表中查找匹配的知识库
    const foundKb = findKnowledgeBaseByName(selectedKnowledgeBase.value.name);
    if (foundKb) {
      console.log('找到匹配的知识库，更新选中值');
      selectedKnowledgeBase.value = JSON.parse(JSON.stringify(foundKb));
    } else {
      console.log('未找到匹配的知识库，保持当前选择');
    }
  } else if (newKbs.length > 0 && !selectedKnowledgeBase.value) {
    console.log('没有选中的知识库，但列表不为空，尝试选择默认知识库');
    const defaultKb = findKnowledgeBaseByName("default") || newKbs[0];
    if (defaultKb) {
      selectedKnowledgeBase.value = JSON.parse(JSON.stringify(defaultKb));
      emit('update:modelValue', selectedKnowledgeBase.value);
    }
  }
}, { deep: true });

// 页面加载时获取知识库列表
onMounted(() => {
  loadKnowledgeBases();
});
</script>

<style scoped>
.w-100 {
  width: 100%;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.text-info {
  color: #909399;
  font-size: 0.9em;
}
</style>

<style>
/* 全局样式，确保下拉菜单正常显示 */
.kb-selector-dropdown {
  z-index: 3000 !important;
}
</style>
