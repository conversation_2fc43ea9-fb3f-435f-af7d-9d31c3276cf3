<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建新的知识库"
    width="30%"
    :close-on-click-modal="false"
    @closed="resetForm"
  >
    <el-form :model="form" label-width="80px" @submit.prevent="createKnowledgeBase">
      <el-form-item label="知识库名称" required>
        <el-input 
          v-model="form.kbName" 
          placeholder="请输入知识库名称"
          :disabled="isLoading"
        />
      </el-form-item>

      <div v-if="status" class="mb-3">
        <el-alert
          :title="statusMessage"
          :type="status"
          :closable="false"
          show-icon
        />
      </div>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeModal">取消</el-button>
        <el-button 
          type="primary" 
          @click="createKnowledgeBase" 
          :loading="isLoading"
        >
          创建
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
/**
 * 创建知识库模态框组件
 * 提供创建新知识库的表单界面和功能
 */
import { ref, watch } from 'vue';
import { useKnowledgeBaseStore } from '@/stores/knowledgeBase';

const props = defineProps({
  isOpen: Boolean,
});

const emit = defineEmits(['close', 'created']);

/**
 * 知识库存储实例
 * 用于创建新的知识库
 */
const kbStore = useKnowledgeBaseStore();

/**
 * 对话框显示状态
 * 根据父组件传入的isOpen属性控制
 */
const dialogVisible = ref(false);

/**
 * 表单数据对象
 * 包含创建知识库所需的字段
 */
const form = ref({
  kbName: ''
});

/**
 * 表单状态变量
 */
const status = ref(''); // 'success', 'warning', 'error'
const statusMessage = ref('');
const isLoading = ref(false);

/**
 * 监听父组件传入的isOpen属性变化
 * 同步更新本地dialogVisible状态
 */
watch(() => props.isOpen, (val) => {
  dialogVisible.value = val;
});

/**
 * 监听本地dialogVisible状态变化
 * 当对话框关闭时，通知父组件
 */
watch(dialogVisible, (val) => {
  if (!val) {
    emit('close');
  }
});

/**
 * 创建知识库
 * 向后端发送创建知识库请求并处理响应
 */
const createKnowledgeBase = async () => {
  if (!form.value.kbName) {
    status.value = 'warning';
    statusMessage.value = '请输入知识库名称';
    return;
  }
  
  try {
    isLoading.value = true;
    status.value = 'info';
    statusMessage.value = '正在创建...';
    
    const result = await kbStore.createKnowledgeBase(form.value.kbName);
    
    if (result.status === 'success') {
      status.value = 'success';
      statusMessage.value = result.message || '创建成功！';
      
      // 重新加载知识库列表
      await kbStore.fetchKnowledgeBases();
      
      // 通知父组件创建成功
      emit('created', { name: form.value.kbName });
      
      // 延迟关闭模态框
      setTimeout(() => {
        closeModal();
      }, 1500);
    } else {
      status.value = 'error';
      statusMessage.value = result.message || '创建失败，请重试';
    }
  } catch (error) {
    status.value = 'error';
    statusMessage.value = error.message || '创建失败，请重试';
  } finally {
    isLoading.value = false;
  }
};

/**
 * 重置表单状态
 * 清空表单数据和状态信息
 */
const resetForm = () => {
  form.value.kbName = '';
  status.value = '';
  statusMessage.value = '';
};

/**
 * 关闭模态框
 * 设置dialogVisible为false，触发watch监听器通知父组件
 */
const closeModal = () => {
  dialogVisible.value = false;
};
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
