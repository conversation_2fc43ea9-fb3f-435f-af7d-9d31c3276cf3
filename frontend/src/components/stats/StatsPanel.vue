<template>
  <el-card shadow="hover" class="stats-card">
    <el-skeleton :loading="loading" animated>
      <template #template>
        <div class="stats-container">
          <div class="current-kb-section">
            <el-skeleton-item variant="p" style="width: 100%; height: 60px" />
          </div>
          <div class="stats-grid">
            <el-skeleton-item variant="p" style="width: 100%; height: 100px" />
            <el-skeleton-item variant="p" style="width: 100%; height: 100px" />
            <el-skeleton-item variant="p" style="width: 100%; height: 100px" />
          </div>
        </div>
      </template>
      
      <template #default>
        <div class="stats-container">
          <!-- 当前知识库区域 - 单独显示在顶部 -->
          <div class="current-kb-section">
            <div class="current-kb-header">
              <el-icon class="kb-icon"><Reading /></el-icon>
              <span>当前知识库</span>
            </div>
            <div class="current-kb-value">{{ stats.current_kb || '未选择知识库' }}</div>
          </div>
          
          <!-- 数值统计区域 - 三等分布局 -->
          <div class="stats-grid">
            <el-card shadow="hover" class="stat-item">
              <div class="stat-content">
                <el-icon class="stat-icon"><Collection /></el-icon>
                <div class="stat-info">
                  <div class="stat-value">{{ stats.kb_count || 0 }}</div>
                  <div class="stat-label">知识库数量</div>
                </div>
              </div>
            </el-card>
            
            <el-card shadow="hover" class="stat-item">
              <div class="stat-content">
                <el-icon class="stat-icon"><Document /></el-icon>
                <div class="stat-info">
                  <div class="stat-value">{{ stats.total_documents || 0 }}</div>
                  <div class="stat-label">文档数量</div>
                </div>
              </div>
            </el-card>
            
            <el-card shadow="hover" class="stat-item">
              <div class="stat-content">
                <el-icon class="stat-icon"><Files /></el-icon>
                <div class="stat-info">
                  <div class="stat-value">{{ stats.total_chunks || 0 }}</div>
                  <div class="stat-label">文本块数量</div>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </template>
    </el-skeleton>
  </el-card>
</template>

<script setup>
/**
 * 统计信息面板组件
 * 显示系统的统计数据，包括知识库、文档和文本块数量
 */
import { ref, onMounted, onUnmounted } from 'vue';
import { Collection, Document, Files, Reading } from '@element-plus/icons-vue';
import { useStatsStore } from '@/stores/stats';

// 状态
const stats = ref({
  kb_count: 0,
  total_documents: 0,
  total_chunks: 0,
  current_kb: ''
});
const loading = ref(true);
const statsStore = useStatsStore();
let refreshInterval;

/**
 * 加载统计数据
 * 从后端获取最新的统计信息
 */
const loadStats = async () => {
  loading.value = true;
  try {
    const result = await statsStore.fetchStats();
    console.log('获取到的统计数据:', result);
    
    // 确保我们使用正确的数据结构
    if (result && typeof result === 'object') {
      // 直接使用 statsStore 中的状态，它已经在 fetchStats 中被更新
      stats.value = {
        kb_count: statsStore.kb_count,
        total_documents: statsStore.total_documents,
        total_chunks: statsStore.total_chunks,
        current_kb: statsStore.current_kb
      };
      console.log('更新后的统计数据:', stats.value);
    } else {
      console.error('统计数据格式不正确:', result);
    }
  } catch (error) {
    console.error('获取统计数据失败', error);
  } finally {
    loading.value = false;
  }
};

/**
 * 创建知识库变更事件处理函数
 */
const handleKnowledgeBaseChanged = () => {
  loadStats();
};

onMounted(() => {
  // 初次加载统计数据
  loadStats();
  
  // 每30秒刷新一次
  refreshInterval = setInterval(loadStats, 30000);
  
  // 监听知识库变更事件
  window.addEventListener('knowledgeBaseChanged', handleKnowledgeBaseChanged);
});

onUnmounted(() => {
  // 清理定时器和事件监听器
  clearInterval(refreshInterval);
  window.removeEventListener('knowledgeBaseChanged', handleKnowledgeBaseChanged);
});
</script>

<style scoped>
.stats-card {
  margin-bottom: 20px;
}

.stats-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 当前知识库区域样式 */
.current-kb-section {
  background-color: rgba(65, 215, 19, 0.1); /* 适应新主题色的浅色背景 */
  border-radius: 8px;
  padding: 12px 16px;
  border-left: 4px solid #41D713; /* 使用新的主题色 */
}

.current-kb-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  color: #41D713; /* 使用新的主题色 */
  font-weight: 500;
}

.kb-icon {
  font-size: 18px;
}

.current-kb-value {
  font-size: 18px;
  word-break: break-word;
  color: var(--el-text-color-primary);
}

/* 数值统计区域样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

.stat-item {
  transition: all 0.3s;
  border-radius: 8px;
  overflow: hidden;
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(65, 215, 19, 0.2); /* 适应新主题色的阴影 */
  border: 1px solid rgba(65, 215, 19, 0.3); /* 添加主题色边框 */
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 16px;
}

.stat-icon {
  font-size: 36px;
  color: #41D713; /* 使用新的主题色 */
  background-color: rgba(65, 215, 19, 0.1); /* 适应新主题色的浅色背景 */
  padding: 12px;
  border-radius: 8px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #41D713; /* 使用新的主题色 */
  line-height: 1.2;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}
</style>
