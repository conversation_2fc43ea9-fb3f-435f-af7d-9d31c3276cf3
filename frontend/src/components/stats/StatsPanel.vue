<template>
  <el-card shadow="hover" class="stats-card">
    <div class="stats-grid">
      <el-skeleton :loading="loading" animated>
        <template #template>
          <div class="stats-grid">
            <el-skeleton-item variant="p" style="width: 100%; height: 100px" />
            <el-skeleton-item variant="p" style="width: 100%; height: 100px" />
            <el-skeleton-item variant="p" style="width: 100%; height: 100px" />
            <el-skeleton-item variant="p" style="width: 100%; height: 100px" />
          </div>
        </template>
        
        <template #default>
          <div class="stats-grid">
            <el-card shadow="never" class="stat-item">
              <template #header>
                <div class="stat-header">
                  <span>知识库数量</span>
                  <el-icon><Collection /></el-icon>
                </div>
              </template>
              <div class="stat-value">{{ stats.kb_count || 0 }}</div>
            </el-card>
            
            <el-card shadow="never" class="stat-item">
              <template #header>
                <div class="stat-header">
                  <span>文档数量</span>
                  <el-icon><Document /></el-icon>
                </div>
              </template>
              <div class="stat-value">{{ stats.total_documents || 0 }}</div>
            </el-card>
            
            <el-card shadow="never" class="stat-item">
              <template #header>
                <div class="stat-header">
                  <span>文本块数量</span>
                  <el-icon><Files /></el-icon>
                </div>
              </template>
              <div class="stat-value">{{ stats.total_chunks || 0 }}</div>
            </el-card>
            
            <el-card shadow="never" class="stat-item">
              <template #header>
                <div class="stat-header">
                  <span>当前知识库</span>
                  <el-icon><Reading /></el-icon>
                </div>
              </template>
              <div class="stat-value current-kb">{{ stats.current_kb || '无' }}</div>
            </el-card>
          </div>
        </template>
      </el-skeleton>
    </div>
  </el-card>
</template>

<script setup>
/**
 * 统计信息面板组件
 * 显示系统的统计数据，包括知识库、文档和文本块数量
 */
import { ref, onMounted, onUnmounted } from 'vue';
import { Collection, Document, Files, Reading } from '@element-plus/icons-vue';
import { useStatsStore } from '@/stores/stats';

// 状态
const stats = ref({
  kb_count: 0,
  total_documents: 0,
  total_chunks: 0,
  current_kb: ''
});
const loading = ref(true);
const statsStore = useStatsStore();
let refreshInterval;

/**
 * 加载统计数据
 * 从后端获取最新的统计信息
 */
const loadStats = async () => {
  loading.value = true;
  try {
    const result = await statsStore.fetchStats();
    stats.value = result;
  } catch (error) {
    console.error('获取统计数据失败', error);
  } finally {
    loading.value = false;
  }
};

/**
 * 创建知识库变更事件处理函数
 */
const handleKnowledgeBaseChanged = () => {
  loadStats();
};

onMounted(() => {
  // 初次加载统计数据
  loadStats();
  
  // 每30秒刷新一次
  refreshInterval = setInterval(loadStats, 30000);
  
  // 监听知识库变更事件
  window.addEventListener('knowledgeBaseChanged', handleKnowledgeBaseChanged);
});

onUnmounted(() => {
  // 清理定时器和事件监听器
  clearInterval(refreshInterval);
  window.removeEventListener('knowledgeBaseChanged', handleKnowledgeBaseChanged);
});
</script>

<style scoped>
.stats-card {
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

.stat-item {
  transition: transform 0.3s;
}

.stat-item:hover {
  transform: translateY(-5px);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: var(--el-color-primary);
  margin-top: 8px;
  text-align: center;
}

.current-kb {
  font-size: 20px;
  word-break: break-word;
}
</style>
