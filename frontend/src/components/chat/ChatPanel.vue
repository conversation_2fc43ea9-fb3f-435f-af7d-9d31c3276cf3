<template>
  <el-card class="chat-panel" shadow="hover">
    <template #header>
      <div class="chat-header">
        <h3>智能问答</h3>
      </div>
    </template>
    
    <!-- 问题输入区域 -->
    <div class="question-area">
      <el-form @submit.prevent="submitQuestion">
        <el-input
          v-model="question"
          placeholder="请输入您的问题..."
          :disabled="isLoading"
          clearable
          class="question-input"
        >
          <template #append>
            <el-button
              type="primary"
              :icon="Search"
              :loading="isLoading"
              @click="submitQuestion"
              :disabled="!question.trim() || isLoading"
            >提问</el-button>
          </template>
        </el-input>
      </el-form>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-area">
      <el-skeleton :rows="6" animated />
    </div>
    
    <!-- 回答内容 -->
    <div v-if="answer && !isLoading" class="answer-area">
      <el-divider content-position="left">回答</el-divider>
      
      <!-- 回答内容 -->
      <div class="answer-content markdown-content" v-html="renderedAnswer"></div>
      
      <!-- 来源文档 -->
      <div v-if="sources && sources.length > 0" class="sources-area">
        <el-divider content-position="left">相关文档</el-divider>
        
        <el-collapse>
          <el-collapse-item
            v-for="(source, index) in sources"
            :key="index"
            :title="`来源: ${source.source} (第 ${source.page} 页)`"
          >
            <div class="source-content">{{ source.content }}</div>
          </el-collapse-item>
        </el-collapse>
      </div>
      
      <!-- 错误信息 -->
      <el-alert
        v-if="error"
        :title="error"
        type="error"
        show-icon
        :closable="false"
        class="error-message"
      />
    </div>
    
    <!-- 引导提示 -->
    <div v-if="!answer && !isLoading" class="empty-state">
      <el-empty description="请输入您的问题，AI助手将为您回答">
        <template #image>
          <el-icon :size="64"><ChatDotRound /></el-icon>
        </template>
      </el-empty>
    </div>
  </el-card>
</template>

<script setup>
/**
 * 聊天/问答面板组件
 * 提供用户问题输入和AI回答显示功能
 */
import { ref, computed } from 'vue';
import { Search, ChatDotRound } from '@element-plus/icons-vue';
import { useQueryStore } from '@/stores/query';
import { marked } from 'marked';

// 状态
const question = ref('');
const answer = ref('');
const sources = ref([]);
const error = ref('');
const isLoading = ref(false);

// 存储实例
const queryStore = useQueryStore();

/**
 * 渲染Markdown格式的回答
 * 将AI返回的Markdown格式文本转换为HTML
 */
const renderedAnswer = computed(() => {
  if (!answer.value) return '';
  return marked.parse(answer.value);
});

/**
 * 提交问题
 * 向后端发送用户问题并获取AI回答
 */
const submitQuestion = async () => {
  if (!question.value.trim() || isLoading.value) return;
  
  try {
    isLoading.value = true;
    error.value = '';
    
    const result = await queryStore.submitQuery(question.value);
    
    // 处理结果
    answer.value = result.answer || '';
    sources.value = result.sources || [];
    error.value = result.error || '';
    
    // 如果请求成功，保留问题以便用户查看
    if (!error.value) {
      // 可选：清空问题输入框，取决于产品设计决策
      // question.value = '';
    }
  } catch (err) {
    error.value = err.message || '查询失败，请重试';
    answer.value = '';
    sources.value = [];
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
.chat-panel {
  margin-bottom: 20px;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.question-area {
  margin-bottom: 20px;
}

.question-input {
  width: 100%;
}

.loading-area {
  padding: 20px 0;
}

.answer-area {
  margin-top: 20px;
}

.answer-content {
  padding: 16px;
  background-color: var(--el-fill-color-light);
  border-radius: 8px;
  line-height: 1.6;
}

.sources-area {
  margin-top: 20px;
}

.source-content {
  padding: 12px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 4px;
  font-size: 14px;
}

.error-message {
  margin-top: 20px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
  color: var(--el-text-color-secondary);
}
</style>

<style>
/* 全局样式，不使用scoped */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 500;
}

.markdown-content h1 {
  font-size: 1.8em;
}

.markdown-content h2 {
  font-size: 1.6em;
}

.markdown-content h3 {
  font-size: 1.4em;
}

.markdown-content p {
  margin: 1em 0;
}

.markdown-content code {
  background-color: var(--el-fill-color);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: SFMono-Regular, Consolas, Liberation Mono, Menlo, monospace;
}

.markdown-content pre {
  background-color: var(--el-fill-color);
  padding: 16px;
  border-radius: 4px;
  overflow-x: auto;
}

.markdown-content pre code {
  background-color: transparent;
  padding: 0;
}

.markdown-content ul,
.markdown-content ol {
  padding-left: 2em;
  margin: 1em 0;
}

.markdown-content blockquote {
  margin: 1em 0;
  padding: 0 1em;
  color: var(--el-text-color-secondary);
  border-left: 0.25em solid var(--el-border-color);
}

.markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

.markdown-content table th,
.markdown-content table td {
  border: 1px solid var(--el-border-color);
  padding: 8px;
}

.markdown-content table th {
  background-color: var(--el-fill-color-light);
}
</style>
