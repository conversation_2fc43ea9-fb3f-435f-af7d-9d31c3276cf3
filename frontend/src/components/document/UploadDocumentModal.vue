<template>
  <el-dialog
    v-model="dialogVisible"
    title="上传文档到知识库"
    width="40%"
    :close-on-click-modal="false"
    @closed="resetForm"
  >
    <el-form :model="form" label-width="80px">
      <!-- 知识库选择（单选框组） -->
      <el-form-item label="知识库" required>
        <el-radio-group v-model="selectedKnowledgeBaseName" @change="onKnowledgeBaseRadioChange">
          <el-radio 
            v-for="kb in knowledgeBases" 
            :key="kb.id || kb.name" 
            :label="kb.name"
            border
          >
            {{ kb.name }} (文档数: {{ kb.document_count }})
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 文件上传区域 -->
      <el-form-item label="选择文件" required>
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          action="#"
          multiple
          :auto-upload="false"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :limit="10"
          :file-list="fileList"
          :accept="'.pdf,.txt,.md'"
          :before-upload="beforeUpload"
        >
          <el-icon class="el-icon--upload"><Upload /></el-icon>
          <div class="el-upload__text">
            <em>拖放文件到此处或<em>点击上传</em></em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              <b>仅支持以下格式文件：</b>
              <el-tag size="small" type="success" class="file-type-tag">PDF</el-tag>
              <el-tag size="small" type="success" class="file-type-tag">TXT</el-tag>
              <el-tag size="small" type="success" class="file-type-tag">MD</el-tag>
            </div>
          </template>
        </el-upload>
      </el-form-item>

      <!-- 上传状态 -->
      <el-form-item v-if="status">
        <el-alert
          :title="statusMessage"
          :type="status"
          :closable="false"
          show-icon
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeModal">取消</el-button>
        <el-button
          type="primary"
          @click="uploadDocuments"
          :loading="isLoading"
          :disabled="fileList.length === 0 || !selectedKnowledgeBase"
        >
          上传
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
/**
 * 文档上传模态框组件
 * 提供上传文档到知识库的功能
 */
import { ref, watch } from 'vue';
import { Upload } from '@element-plus/icons-vue';
// 不再需要 KnowledgeBaseSelector 组件
import { useDocumentStore } from '@/stores/document';
import { useKnowledgeBaseStore } from '@/stores/knowledgeBase';

const props = defineProps({
  isOpen: Boolean,
});

const emit = defineEmits(['close', 'uploaded']);

/**
 * 文档存储实例
 * 用于上传文档
 */
const documentStore = useDocumentStore();

/**
 * 知识库存储实例
 * 用于获取知识库信息
 */
const kbStore = useKnowledgeBaseStore();

/**
 * 对话框显示状态
 * 根据父组件传入的isOpen属性控制
 */
const dialogVisible = ref(false);

/**
 * 表单数据对象
 */
const form = ref({});

/**
 * 上传相关对象和状态
 */
const uploadRef = ref(null);
const fileList = ref([]);
const knowledgeBases = ref([]);
const selectedKnowledgeBase = ref(null);
const selectedKnowledgeBaseName = ref('');
const status = ref(''); // 'success', 'warning', 'info', 'error'
const statusMessage = ref('');
const isLoading = ref(false);

/**
 * 处理知识库单选框选择变化
 * @param {String} kbName - 选中的知识库名称
 */
const onKnowledgeBaseRadioChange = (kbName) => {
  console.log('知识库选择变化:', kbName);
  if (kbName) {
    // 根据名称查找完整的知识库对象
    const kb = knowledgeBases.value.find(item => item.name === kbName);
    if (kb) {
      selectedKnowledgeBase.value = kb;
      console.log('已选择知识库:', selectedKnowledgeBase.value);
    }
  }
};

/**
 * 监听父组件传入的isOpen属性变化
 * 同步更新本地dialogVisible状态
 */
watch(() => props.isOpen, async (val) => {
  console.log('模态框打开状态变化:', val);
  dialogVisible.value = val;

  if (val) {
    console.log('模态框打开，准备初始化知识库选择');

    // 模态框打开时，先清空选中的知识库
    selectedKnowledgeBase.value = null;
    selectedKnowledgeBaseName.value = '';

    // 主动获取知识库列表
    try {
      console.log('模态框打开时加载知识库列表');
      await kbStore.fetchKnowledgeBases();
      
      // 获取并设置知识库列表
      knowledgeBases.value = kbStore.knowledgeBases;
      console.log('获取到知识库列表:', knowledgeBases.value);

      // 尝试自动选择知识库
      if (knowledgeBases.value.length > 0) {
        console.log('尝试自动选择知识库');
        // 优先选择default知识库，其次是当前知识库，最后是第一个知识库
        const defaultKb = knowledgeBases.value.find(kb => kb.name === 'default');
        const currentKb = knowledgeBases.value.find(kb => kb.is_current);
        const kbToSelect = defaultKb || currentKb || knowledgeBases.value[0];

        if (kbToSelect) {
          console.log('自动选择知识库:', kbToSelect.name);
          selectedKnowledgeBase.value = kbToSelect;
          selectedKnowledgeBaseName.value = kbToSelect.name;
        }
      }
    } catch (error) {
      console.error('加载知识库列表失败:', error);
    }
  }
});

/**
 * 监听本地dialogVisible状态变化
 * 当对话框关闭时，通知父组件
 */
watch(dialogVisible, (val) => {
  if (!val) {
    emit('close');
  }
});

/**
 * 上传前检查文件类型
 * @param {Object} file - 要上传的文件
 * @returns {boolean|Promise} - 返回true表示允许上传，返回false表示不允许上传
 */
const beforeUpload = (file) => {
  // 检查文件类型
  const fileName = file.name.toLowerCase();
  const validExtensions = ['.pdf', '.txt', '.md'];
  const isValidType = validExtensions.some(ext => fileName.endsWith(ext));
  
  if (!isValidType) {
    status.value = 'warning';
    statusMessage.value = `不支持的文件类型: ${file.name}。仅支持 PDF、TXT、MD 格式文件。`;
    return false;
  }
  
  return true;
};

/**
 * 处理文件变化
 * 当使用Element Plus的el-upload组件添加文件时触发
 * @param {Object} file - 文件对象
 * @param {Array} files - 当前的文件列表
 */
const handleFileChange = (file, files) => {
  console.log('文件变化:', file.name, '当前文件列表长度:', files.length);
  // 更新文件列表
  fileList.value = files;
  // 清除之前的状态信息
  if (status.value === 'warning' || status.value === 'error') {
    status.value = '';
    statusMessage.value = '';
  }
  return true;
};

/**
 * 处理文件移除
 * 当从文件列表中移除文件时触发
 * @param {Object} file - 被移除的文件对象
 * @param {Array} fileList - 当前的文件列表
 */
const handleFileRemove = (file, fileList) => {
  // 移除文件时的处理逻辑
};

/**
 * 上传文档
 * 将选中的文件上传到指定知识库
 */
const uploadDocuments = async () => {
  console.log('准备上传文档，当前选中知识库:', selectedKnowledgeBase.value);
  console.log('当前文件列表:', fileList.value);

  if (!selectedKnowledgeBase.value || fileList.value.length === 0) {
    status.value = 'warning';
    statusMessage.value = '请选择知识库和文件';
    return;
  }

  try {
    isLoading.value = true;
    status.value = 'info';
    statusMessage.value = '正在上传...';

    // 再次检查文件类型
    const invalidFiles = fileList.value.filter(file => {
      const fileName = file.name.toLowerCase();
      return !(['.pdf', '.txt', '.md'].some(ext => fileName.endsWith(ext)));
    });
    
    if (invalidFiles.length > 0) {
      const fileNames = invalidFiles.map(f => f.name).join(', ');
      throw new Error(`不支持的文件类型: ${fileNames}。仅支持 PDF、TXT、MD 格式文件。`);
    }

    const formData = new FormData();

    // 确保知识库名称正确获取
    const kbName = selectedKnowledgeBase.value.name;
    if (!kbName) {
      throw new Error('知识库名称无效');
    }

    console.log('上传到知识库:', kbName);
    formData.append('kb_name', kbName);

    // 添加所有文件到表单数据
    if (fileList.value.length === 0) {
      throw new Error('请选择至少一个文件');
    }

    fileList.value.forEach(file => {
      // el-upload 组件的文件对象中，原始文件在 raw 属性中
      if (file.raw) {
        console.log('添加文件:', file.name);
        formData.append('files', file.raw);
      } else {
        console.warn('文件对象缺少raw属性:', file.name);
      }
    });

    console.log('开始上传文件...');
    const result = await documentStore.uploadDocuments(formData);
    console.log('上传结果:', result);

    if (result.status === 'success') {
      status.value = 'success';
      statusMessage.value = result.message || '上传成功！';

      // 重新加载知识库数据
      console.log('重新加载知识库数据');
      await kbStore.fetchKnowledgeBases();

      // 通知父组件上传成功
      emit('uploaded');

      // 延迟关闭模态框
      setTimeout(() => {
        closeModal();
      }, 2000);
    } else {
      status.value = 'error';
      statusMessage.value = result.message || '上传失败，请重试';
    }
  } catch (error) {
    console.error('上传文档时发生错误:', error);
    status.value = 'error';
    statusMessage.value = error.message || '上传失败，请重试';
  } finally {
    isLoading.value = false;
  }
};

/**
 * 重置表单
 * 清空文件列表和状态信息
 */
const resetForm = () => {
  console.log('重置表单');
  fileList.value = [];
  status.value = '';
  statusMessage.value = '';

  // 延迟重置知识库选择，避免与其他操作冲突
  setTimeout(() => {
    console.log('重置知识库选择');
    selectedKnowledgeBase.value = null; // 重置选中的知识库
    selectedKnowledgeBaseName.value = ''; // 重置选中的知识库名称
  }, 100);

  if (uploadRef.value) {
    uploadRef.value.clearFiles();
  }
};

/**
 * 关闭模态框
 * 设置dialogVisible为false，触发watch监听器通知父组件
 */
const closeModal = () => {
  dialogVisible.value = false;
};
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.upload-demo {
  width: 100%;
}

.el-radio-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.el-radio {
  margin-right: 0;
  margin-bottom: 10px;
}

.file-type-tag {
  margin-right: 5px;
  margin-left: 5px;
}

.el-upload__tip {
  line-height: 1.8;
  margin-top: 10px;
}
</style>
