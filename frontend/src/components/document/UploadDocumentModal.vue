<template>
  <el-dialog
    v-model="dialogVisible"
    title="上传文档到知识库"
    width="40%"
    :close-on-click-modal="false"
    @closed="resetForm"
  >
    <el-form :model="form" label-width="80px">
      <!-- 知识库选择 -->
      <el-form-item label="知识库" required>
        <KnowledgeBaseSelector v-model="selectedKnowledgeBase" />
      </el-form-item>
      
      <!-- 文件上传区域 -->
      <el-form-item label="选择文件" required>
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          action="#"
          multiple
          :auto-upload="false"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :limit="10"
          :file-list="fileList"
        >
          <el-icon class="el-icon--upload"><Upload /></el-icon>
          <div class="el-upload__text">
            <em>拖放文件到此处或<em>点击上传</em></em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 PDF、TXT、MD 格式文件
            </div>
          </template>
        </el-upload>
      </el-form-item>
      
      <!-- 上传状态 -->
      <el-form-item v-if="status">
        <el-alert
          :title="statusMessage"
          :type="status"
          :closable="false"
          show-icon
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeModal">取消</el-button>
        <el-button 
          type="primary" 
          @click="uploadDocuments" 
          :loading="isLoading"
          :disabled="fileList.length === 0 || !selectedKnowledgeBase"
        >
          上传
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
/**
 * 文档上传模态框组件
 * 提供上传文档到知识库的功能
 */
import { ref, watch } from 'vue';
import { Upload } from '@element-plus/icons-vue';
import KnowledgeBaseSelector from '../knowledge-base/KnowledgeBaseSelector.vue';
import { useDocumentStore } from '@/stores/document';
import { useKnowledgeBaseStore } from '@/stores/knowledgeBase';

const props = defineProps({
  isOpen: Boolean,
});

const emit = defineEmits(['close', 'uploaded']);

/**
 * 文档存储实例
 * 用于上传文档
 */
const documentStore = useDocumentStore();

/**
 * 知识库存储实例
 * 用于获取知识库信息
 */
const kbStore = useKnowledgeBaseStore();

/**
 * 对话框显示状态
 * 根据父组件传入的isOpen属性控制
 */
const dialogVisible = ref(false);

/**
 * 表单数据对象
 */
const form = ref({});

/**
 * 上传相关对象和状态
 */
const uploadRef = ref(null);
const fileList = ref([]);
const selectedKnowledgeBase = ref(null);
const status = ref(''); // 'success', 'warning', 'info', 'error'
const statusMessage = ref('');
const isLoading = ref(false);

/**
 * 监听父组件传入的isOpen属性变化
 * 同步更新本地dialogVisible状态
 */
watch(() => props.isOpen, (val) => {
  dialogVisible.value = val;
});

/**
 * 监听本地dialogVisible状态变化
 * 当对话框关闭时，通知父组件
 */
watch(dialogVisible, (val) => {
  if (!val) {
    emit('close');
  }
});

/**
 * 处理文件变化
 * 当使用Element Plus的el-upload组件添加文件时触发
 * @param {Object} file - 文件对象
 */
const handleFileChange = (file) => {
  // Element Plus的上传组件会自动管理文件列表
  // 这里可以进行额外的验证
  return true;
};

/**
 * 处理文件移除
 * 当从文件列表中移除文件时触发
 * @param {Object} file - 被移除的文件对象
 * @param {Array} fileList - 当前的文件列表
 */
const handleFileRemove = (file, fileList) => {
  // 移除文件时的处理逻辑
};

/**
 * 上传文档
 * 将选中的文件上传到指定知识库
 */
const uploadDocuments = async () => {
  if (!selectedKnowledgeBase.value || fileList.value.length === 0) {
    status.value = 'warning';
    statusMessage.value = '请选择知识库和文件';
    return;
  }
  
  try {
    isLoading.value = true;
    status.value = 'info';
    statusMessage.value = '正在上传...';
    
    const formData = new FormData();
    formData.append('kb_name', selectedKnowledgeBase.value.name);
    
    // 添加所有文件到表单数据
    fileList.value.forEach(file => {
      // el-upload 组件的文件对象中，原始文件在 raw 属性中
      formData.append('files', file.raw);
    });
    
    const result = await documentStore.uploadDocuments(formData);
    
    if (result.status === 'success') {
      status.value = 'success';
      statusMessage.value = result.message || '上传成功！';
      
      // 重新加载知识库数据
      await kbStore.fetchKnowledgeBases();
      
      // 通知父组件上传成功
      emit('uploaded');
      
      // 延迟关闭模态框
      setTimeout(() => {
        closeModal();
      }, 2000);
    } else {
      status.value = 'error';
      statusMessage.value = result.message || '上传失败，请重试';
    }
  } catch (error) {
    status.value = 'error';
    statusMessage.value = error.message || '上传失败，请重试';
  } finally {
    isLoading.value = false;
  }
};

/**
 * 重置表单
 * 清空文件列表和状态信息
 */
const resetForm = () => {
  fileList.value = [];
  status.value = '';
  statusMessage.value = '';
  if (uploadRef.value) {
    uploadRef.value.clearFiles();
  }
};

/**
 * 关闭模态框
 * 设置dialogVisible为false，触发watch监听器通知父组件
 */
const closeModal = () => {
  dialogVisible.value = false;
};
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.upload-demo {
  width: 100%;
}
</style>
