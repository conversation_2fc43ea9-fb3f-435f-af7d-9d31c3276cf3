@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义全局样式 */
@layer base {
  body {
    @apply bg-gray-100 text-gray-900;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-medium;
  }
}

/* 自定义组件样式 */
@layer components {
  .btn {
    @apply px-4 py-2 rounded-md text-sm font-medium transition-colors;
  }
  
  .btn-primary {
    @apply bg-indigo-600 text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500;
  }
  
  .btn-secondary {
    @apply bg-white text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500;
  }
  
  .card {
    @apply bg-white rounded-lg shadow p-6;
  }
}
