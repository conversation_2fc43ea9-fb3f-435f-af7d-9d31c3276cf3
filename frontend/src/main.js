import { createApp } from 'vue';
import { createPinia } from 'pinia';
import ElementPlus from 'element-plus';
import zhCn from 'element-plus/dist/locale/zh-cn.mjs';
import 'element-plus/dist/index.css';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import App from './App.vue';
import router from './router';
import './assets/index.scss';
import { marked } from 'marked';

/**
 * 配置 marked 处理中文等字符
 * 启用 GitHub Flavored Markdown
 * 支持换行符转换为 <br>
 * 启用智能列表识别
 */
marked.setOptions({
  gfm: true,
  breaks: true,
  sanitize: false,
  smartLists: true,
  smartypants: false,
  xhtml: false
});

// 创建Vue应用实例
const app = createApp(App);

// 注册所有 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

// 使用 Element Plus 组件库，并设置中文语言
app.use(ElementPlus, {
  locale: zhCn,
});

// 使用Pinia进行状态管理
app.use(createPinia());

// 使用Vue Router进行路由管理
app.use(router);

// 挂载应用到DOM
app.mount('#app');
