/**
 * 知识库相关的 API 接口
 */
import axios from 'axios';

// 基础 API 路径
const BASE_URL = '/api';

export const knowledgeBaseApi = {
  /**
   * 获取所有知识库列表
   * @returns {Promise} 返回知识库列表
   */
  getAll() {
    return axios.get(`${BASE_URL}/knowledge-bases`);
  },

  /**
   * 创建新知识库
   * @param {string} name - 知识库名称
   * @returns {Promise} 返回创建结果
   */
  create(name) {
    return axios.post(`${BASE_URL}/knowledge-bases`, {
      name: name
    });
  },

  /**
   * 切换当前知识库
   * @param {string} name - 知识库名称
   * @returns {Promise} 返回切换结果
   */
  setCurrent(name) {
    return axios.post(`${BASE_URL}/switch-kb`, {
      name: name
    });
  },

  /**
   * 删除知识库
   * @param {string} name - 知识库名称
   * @returns {Promise} 返回删除结果
   */
  delete(name) {
    return axios.delete(`${BASE_URL}/knowledge-bases/${name}`);
  },

  /**
   * 获取知识库统计信息
   * @returns {Promise} 返回统计信息
   */
  getStats() {
    return axios.get(`${BASE_URL}/stats`);
  }
};

export default knowledgeBaseApi;
