import axios from 'axios';

/**
 * 统一导出 API 模块
 */
export { knowledgeBaseApi } from './knowledgeBase';
export { queryApi } from './query';
export { documentApi } from './document';

// 设置 axios 默认配置
axios.defaults.headers.common['Content-Type'] = 'application/json';
axios.defaults.baseURL = '/api';

// 添加响应拦截器
axios.interceptors.response.use(
  response => response,
  error => {
    console.error('请求出错:', error);
    return Promise.reject(error);
  }
);

/**
 * 统计相关API
 */
export const statsApi = {
  /**
   * 获取系统统计信息
   * @returns {Promise} 返回统计信息
   */
  getStats() {
    return axios.get('/stats');
  }
};
