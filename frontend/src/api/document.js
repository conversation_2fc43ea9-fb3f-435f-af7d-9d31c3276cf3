import axios from 'axios';

/**
 * 文档相关API
 */
export const documentApi = {
  /**
   * 上传文档到知识库
   * @param {string} kbName - 知识库名称
   * @param {FileList} files - 文件列表
   * @returns {Promise} 返回上传结果
   */
  upload(kbName, files) {
    const formData = new FormData();
    formData.append('kb_name', kbName);
    
    for (const file of files) {
      formData.append('files', file);
    }
    
    return axios.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }
};
