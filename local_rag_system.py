import os
import json
from typing import List, Dict, Optional, Union
from pathlib import Path
from dotenv import load_dotenv
from langchain_community.document_loaders import (
    DirectoryLoader,
    PyPDFLoader,
    TextLoader,
    UnstructuredMarkdownLoader,
)
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import Chroma
from langchain_ollama import OllamaEmbeddings
from langchain_deepseek import ChatDeepSeek
from langchain.chains import create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain.prompts import ChatPromptTemplate
from langchain.callbacks.manager import CallbackManager
from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler
from langchain_core.output_parsers import StrOutputParser
from langchain_core.documents import Document

# 加载环境变量
load_dotenv()

class KnowledgeBase:
    """知识库类，管理单个知识库的文档和检索"""
    def __init__(self, name: str, embeddings, llm=None):
        """
        初始化知识库
        
        参数:
            name: 知识库名称
            embeddings: 嵌入模型
            llm: 大语言模型，如果为None则创建默认模型
        """
        self.name = name
        self.embeddings = embeddings
        self.llm = llm or ChatDeepSeek(
            api_key=os.getenv("DEEPSEEK_API_KEY"),
            model="deepseek-chat",
            temperature=0.1,
            streaming=True
        )
        self.vector_store = None
        self.retriever = None
        self.qa_chain = None
        self.document_count = 0
        self.chunk_count = 0
        self._initialize_vector_store()
    
    def _initialize_vector_store(self):
        """初始化向量存储"""
        persist_dir = f"./chroma_db/{self.name}"
        os.makedirs(persist_dir, exist_ok=True)
        
        # 如果已存在向量存储，则加载它
        if os.path.exists(os.path.join(persist_dir, 'chroma.sqlite3')):
            try:
                self.vector_store = Chroma(
                    persist_directory=persist_dir,
                    embedding_function=self.embeddings
                )
                # 创建检索器
                self._create_retriever()
                # 创建问答链
                self._create_qa_chain()
            except Exception as e:
                print(f"加载向量存储 {self.name} 时出错: {e}")
                self.vector_store = None
                self.retriever = None
                self.qa_chain = None
            # 计算文档数量
            self._update_stats()
    
    def _update_stats(self):
        """更新知识库统计信息"""
        if not self.vector_store:
            self.chunk_count = 0
            self.document_count = 0
            return
            
        try:
            self.chunk_count = self.vector_store._collection.count()
            # 由于文档可能被分块，所以这只是一个估计
            self.document_count = len(set([m.get("source", "") 
                                      for m in self.vector_store._collection.get(include=["metadatas"])["metadatas"]]))
        except Exception as e:
            print(f"获取知识库 {self.name} 统计信息时出错: {e}")
            self.chunk_count = 0
            self.document_count = 0
    
    def _create_retriever(self):
        """创建检索器"""
        if self.vector_store:
            self.retriever = self.vector_store.as_retriever(
                search_type="mmr",  # 使用最大边际相关性搜索
                search_kwargs={"k": 5}  # 返回前5个最相关的文档块
            )
        
    def load_documents_from_directory(self, directory: str, chunk_size: int = 1000, chunk_overlap: int = 200):
        """
        从目录加载文档到知识库
        
        参数:
            directory: 文档目录路径
            chunk_size: 文本块大小
            chunk_overlap: 文本块重叠大小
        """
        # 支持的文件类型
        loaders = {
            '.pdf': (PyPDFLoader, {}),
            '.txt': (TextLoader, {'encoding': 'utf-8'}),
            '.md': (UnstructuredMarkdownLoader, {}),
        }
        
        # 加载文档
        documents = []
        for ext, (loader_class, loader_args) in loaders.items():
            try:
                loader = DirectoryLoader(
                    directory, 
                    glob=f"**/*{ext}", 
                    loader_cls=loader_class, 
                    loader_kwargs=loader_args,
                    show_progress=True
                )
                documents.extend(loader.load())
            except Exception as e:
                print(f"加载 {ext} 文件时出错: {e}")
        
        if not documents:
            raise ValueError(f"在 {directory} 中未找到支持的文档")
        
        # 添加文档
        self.add_documents(documents, chunk_size, chunk_overlap)
    
    def add_documents(self, documents: List[Document], chunk_size: int = 1000, chunk_overlap: int = 200):
        """
        添加文档到知识库
        
        参数:
            documents: 要添加的文档列表
            chunk_size: 文本块大小
            chunk_overlap: 文本块重叠大小
        """
        if not documents:
            return
            
        # 分割文本
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
            is_separator_regex=False,
        )
        chunks = text_splitter.split_documents(documents)
        
        print(f"已加载 {len(documents)} 个文档，分割为 {len(chunks)} 个文本块")
        
        # 创建或更新向量存储
        persist_dir = f"./chroma_db/{self.name}"
        
        if not self.vector_store:
            # 创建新的向量存储
            self.vector_store = Chroma.from_documents(
                documents=chunks,
                embedding=self.embeddings,
                persist_directory=persist_dir
            )
        else:
            # 添加到现有向量存储
            self.vector_store.add_documents(chunks)
            self.vector_store.persist()
        
        # 创建检索器
        self._create_retriever()
        
        # 创建问答链
        self._create_qa_chain()
        
        # 更新统计信息
        self._update_stats()
        
    def _create_qa_chain(self):
        """创建问答链"""
        # 如果没有检索器，则不创建问答链
        if not self.retriever:
            return
            
        # 自定义提示模板
        template = """使用以下上下文来回答最后的问题。如果你不知道答案，就说你不知道，不要编造答案。
        
        上下文：
        {context}
        
        问题: {input}
        有用的回答:"""
        
        prompt = ChatPromptTemplate.from_template(template)
        
        # 创建问答链
        document_chain = create_stuff_documents_chain(
            self.llm,
            prompt,
            output_parser=StrOutputParser()
        )
        
        # 创建检索链
        self.qa_chain = create_retrieval_chain(
            self.retriever,
            document_chain
        )
    
    def query(self, question: str) -> Dict:
        """
        查询知识库并获取回答
        
        参数:
            question: 用户问题
            
        返回:
            Dict: 包含回答和上下文的字典
        """
        if not self.vector_store:
            return {
                "answer": f"知识库 '{self.name}' 中没有文档，请先上传文档。",
                "sources": [],
                "kb_name": self.name,
                "error": "知识库为空"
            }
            
        # 如果没有问答链，尝试创建
        if not self.qa_chain:
            self._create_qa_chain()
            
        # 如果仍然没有问答链，返回错误
        if not self.qa_chain:
            return {
                "answer": f"无法创建知识库 '{self.name}' 的问答链，请检查知识库是否正确初始化。",
                "sources": [],
                "kb_name": self.name,
                "error": "问答链初始化失败"
            }
            
        try:
            # 使用正确的键名调用检索链
            result = self.qa_chain.invoke({"input": question})
            
            # 整理相关文档信息
            sources = []
            if "context" in result:
                for i, doc in enumerate(result["context"], 1):
                    source = doc.metadata.get("source", "未知来源")
                    page = doc.metadata.get("page", "?")
                    content = doc.page_content[:200] + ("..." if len(doc.page_content) > 200 else "")
                    sources.append({
                        "index": i,
                        "source": source,
                        "page": page,
                        "content": content
                    })
            
            return {
                "answer": result.get("answer", "无法获取回答"),
                "sources": sources,
                "kb_name": self.name
            }
        except Exception as e:
            return {
                "answer": f"查询过程中出错: {str(e)}",
                "sources": [],
                "kb_name": self.name,
                "error": str(e)
            }


class LocalRAGSystem:
    """本地RAG系统，管理多个知识库"""
    
    def __init__(self, model_name: str = "deepseek-chat", embedding_model: str = "bge-m3"):
        """
        初始化RAG系统
        
        参数:
            model_name: 使用的LLM模型名称
            embedding_model: 使用的嵌入模型名称
        """
        # 初始化嵌入模型
        self.embeddings = OllamaEmbeddings(
            model=embedding_model
        )
        
        # 初始化LLM
        self.llm = ChatDeepSeek(
            api_key=os.getenv("DEEPSEEK_API_KEY"),
            model=model_name,
            temperature=0.1,
            streaming=True
        )
        
        # 存储所有知识库
        self.knowledge_bases: Dict[str, KnowledgeBase] = {}
        self.current_kb: Optional[KnowledgeBase] = None
        
        # 确保chroma_db目录存在
        os.makedirs("chroma_db", exist_ok=True)
        
        # 加载现有知识库
        self._load_existing_knowledge_bases()
        
        # 如果没有知识库，创建默认知识库
        if not self.knowledge_bases:
            self.create_knowledge_base("default")
    
    def _load_existing_knowledge_bases(self):
        """加载现有知识库"""
        # 检查chroma_db目录中的所有子目录
        chroma_dir = Path("./chroma_db")
        if not chroma_dir.exists():
            return
            
        for kb_dir in chroma_dir.iterdir():
            if kb_dir.is_dir() and (kb_dir / "chroma.sqlite3").exists():
                kb_name = kb_dir.name
                try:
                    kb = KnowledgeBase(kb_name, self.embeddings, self.llm)
                    self.knowledge_bases[kb_name] = kb
                    # 设置第一个知识库为当前知识库
                    if self.current_kb is None:
                        self.current_kb = kb
                except Exception as e:
                    print(f"加载知识库 {kb_name} 时出错: {e}")
    
    def create_knowledge_base(self, name: str) -> KnowledgeBase:
        """
        创建新的知识库
        
        参数:
            name: 知识库名称
            
        返回:
            KnowledgeBase: 新创建的知识库实例
        """
        if name in self.knowledge_bases:
            raise ValueError(f"知识库 '{name}' 已存在")
        
        kb = KnowledgeBase(name, self.embeddings, self.llm)
        self.knowledge_bases[name] = kb
        
        # 如果没有当前知识库，设置为默认
        if self.current_kb is None:
            self.current_kb = kb
            
        return kb
    
    def get_knowledge_base(self, name: str = None) -> KnowledgeBase:
        """
        获取知识库
        
        参数:
            name: 知识库名称，如果为None则返回当前知识库
            
        返回:
            KnowledgeBase: 知识库实例
        """
        if name is None:
            if self.current_kb is None:
                raise ValueError("没有可用的知识库，请先创建知识库")
            return self.current_kb
            
        if name not in self.knowledge_bases:
            raise ValueError(f"知识库 '{name}' 不存在")
            
        return self.knowledge_bases[name]
    
    def list_knowledge_bases(self) -> List[Dict]:
        """
        获取所有知识库列表
        
        返回:
            List[Dict]: 知识库信息列表，包含名称和统计信息
        """
        return [
            {
                "name": name,
                "document_count": kb.document_count,
                "chunk_count": kb.chunk_count,
                "is_current": kb == self.current_kb
            }
            for name, kb in self.knowledge_bases.items()
        ]
    
    def set_current_knowledge_base(self, name: str) -> None:
        """
        设置当前知识库
        
        参数:
            name: 要设置为当前的知识库名称
        """
        if name not in self.knowledge_bases:
            raise ValueError(f"知识库 '{name}' 不存在")
        self.current_kb = self.knowledge_bases[name]
    
    def load_documents_to_kb(self, directory: str, kb_name: str = None, **kwargs) -> None:
        """
        加载文档到指定知识库
        
        参数:
            directory: 文档目录路径
            kb_name: 目标知识库名称，如果为None则使用当前知识库
            **kwargs: 其他参数，传递给load_documents方法
        """
        kb = self.get_knowledge_base(kb_name)
        kb.load_documents_from_directory(directory, **kwargs)
    
    def query(self, question: str, kb_name: str = None) -> Dict:
        """
        查询知识库
        
        参数:
            question: 用户问题
            kb_name: 要查询的知识库名称，如果为None则使用当前知识库
            
        返回:
            Dict: 包含回答和上下文的字典
        """
        kb = self.get_knowledge_base(kb_name)
        return kb.query(question)


def main():
    """命令行交互式界面"""
    import argparse
    
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description="本地RAG知识库问答系统")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 创建知识库命令
    create_parser = subparsers.add_parser("create", help="创建新的知识库")
    create_parser.add_argument("name", help="知识库名称")
    
    # 加载文档命令
    load_parser = subparsers.add_parser("load", help="加载文档到知识库")
    load_parser.add_argument("--kb", "-k", help="知识库名称（默认为当前知识库）")
    load_parser.add_argument("directory", help="文档目录路径")
    load_parser.add_argument("--chunk-size", type=int, default=1000, help="文本块大小（默认为1000）")
    load_parser.add_argument("--chunk-overlap", type=int, default=200, help="文本块重叠大小（默认为200）")
    
    # 查询命令
    query_parser = subparsers.add_parser("query", help="查询知识库")
    query_parser.add_argument("--kb", "-k", help="知识库名称（默认为当前知识库）")
    query_parser.add_argument("question", nargs="?", help="问题（如果不提供，将进入交互模式）")
    
    # 列出知识库命令
    list_parser = subparsers.add_parser("list", help="列出所有知识库")
    
    # 切换知识库命令
    switch_parser = subparsers.add_parser("switch", help="切换当前知识库")
    switch_parser.add_argument("name", help="要切换到的知识库名称")
    
    # 启动Web服务命令
    web_parser = subparsers.add_parser("web", help="启动Web界面")
    web_parser.add_argument("--host", default="0.0.0.0", help="主机地址（默认为0.0.0.0）")
    web_parser.add_argument("--port", "-p", type=int, default=8000, help="端口号（默认为8000）")
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 检查API密钥
    if not os.getenv("DEEPSEEK_API_KEY"):
        print("错误: 未设置DEEPSEEK_API_KEY环境变量")
        print("请设置环境变量: export DEEPSEEK_API_KEY='your-api-key'")
        return
    
    # 初始化RAG系统
    print("正在初始化RAG系统...")
    rag = LocalRAGSystem()
    
    # 根据命令执行相应操作
    if args.command == "create":
        try:
            kb = rag.create_knowledge_base(args.name)
            print(f"已创建知识库: {args.name}")
        except ValueError as e:
            print(f"错误: {e}")
    
    elif args.command == "load":
        try:
            print(f"正在将文档从 {args.directory} 加载到知识库...")
            rag.load_documents_to_kb(
                args.directory,
                args.kb,
                chunk_size=args.chunk_size,
                chunk_overlap=args.chunk_overlap
            )
            print("文档加载完成！")
        except Exception as e:
            print(f"错误: {e}")
    
    elif args.command == "query":
        if args.question:
            # 单次查询模式
            try:
                result = rag.query(args.question, args.kb)
                print(f"\n回答: {result['answer']}")
                
                print("\n相关文档:")
                for source in result["sources"]:
                    print(f"{source['index']}. {source['source']} (第 {source['page']} 页)")
                    print(f"   内容: {source['content']}")
            except Exception as e:
                print(f"错误: {e}")
        else:
            # 交互式查询模式
            kb_name = args.kb or (
                rag.current_kb.name if rag.current_kb else "未选择知识库"
            )
            print(f"\n当前知识库: {kb_name}")
            print("输入您的问题或输入'退出'结束。")
            
            while True:
                question = input("\n您的问题: ").strip()
                if question.lower() in ['退出', 'exit', 'quit']:
                    break
                    
                if not question:
                    continue
                    
                try:
                    result = rag.query(question, args.kb)
                    print(f"\n回答: {result['answer']}")
                    
                    print("\n相关文档:")
                    for source in result["sources"]:
                        print(f"{source['index']}. {source['source']} (第 {source['page']} 页)")
                        print(f"   内容: {source['content']}")
                except Exception as e:
                    print(f"错误: {e}")
    
    elif args.command == "list":
        kb_list = rag.list_knowledge_bases()
        if not kb_list:
            print("没有可用的知识库")
        else:
            print("\n可用的知识库:")
            for kb_info in kb_list:
                current_mark = "*" if kb_info["is_current"] else " "
                print(f"{current_mark} {kb_info['name']} (文档数: {kb_info['document_count']}, 块数: {kb_info['chunk_count']})")
    
    elif args.command == "switch":
        try:
            rag.set_current_knowledge_base(args.name)
            print(f"已切换到知识库: {args.name}")
        except ValueError as e:
            print(f"错误: {e}")
    
    elif args.command == "web":
        try:
            print(f"正在启动Web界面，访问 http://{args.host}:{args.port} ...")
            import uvicorn
            from app.main import app
            # 设置全局RAG系统实例
            from app.main import set_rag_system
            set_rag_system(rag)
            uvicorn.run("app.main:app", host=args.host, port=args.port, reload=True)
        except ImportError:
            print("错误: 请先安装Web界面依赖，运行 'pip install fastapi uvicorn jinja2 python-multipart'")
        except Exception as e:
            print(f"启动Web界面时出错: {e}")
    
    else:
        # 如果没有提供命令，显示帮助信息
        parser.print_help()


if __name__ == "__main__":
    main()
