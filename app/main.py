from fastapi import FastAPI, Request, Depends, HTTPException, Form, UploadFile, File, APIRouter
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse, RedirectResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, List, Dict, Any, Union
import os
import tempfile
import shutil
from pathlib import Path

# 导入现有的 RAG 系统
from local_rag_system import LocalRAGSystem, KnowledgeBase

# 创建主应用
app = FastAPI(title="海外业务知识库问答系统")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 开发环境允许所有来源，生产环境应该限制为特定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建 API 路由器
api_router = APIRouter(prefix="/api")

# 配置Vue3前端静态文件路径
frontend_dist_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "frontend", "dist")

# 检查前端构建目录是否存在
if not os.path.exists(frontend_dist_path):
    raise RuntimeError(f"前端构建目录不存在: {frontend_dist_path}")

# 检查 index.html 是否存在
index_html_path = os.path.join(frontend_dist_path, "index.html")
if not os.path.exists(index_html_path):
    raise RuntimeError(f"index.html 不存在: {index_html_path}")

# 添加 /app 重定向
@app.get("/app")
async def redirect_to_app_with_slash():
    return RedirectResponse(url="/app/", status_code=307)

# 添加 /app/ 路由
@app.get("/app/")
async def serve_app():
    return FileResponse(index_html_path)

# 全局 RAG 系统实例
_rag_system: Optional[LocalRAGSystem] = None

# 应用根目录
APP_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
print(f"应用根目录: {APP_ROOT}")

def get_rag_system() -> LocalRAGSystem:
    """获取 RAG 系统实例"""
    global _rag_system
    if _rag_system is None:
        print("初始化 RAG 系统实例...")
        # 确保当前工作目录正确
        original_cwd = os.getcwd()
        try:
            # 切换到应用根目录
            os.chdir(APP_ROOT)
            print(f"已切换工作目录到: {os.getcwd()}")
            _rag_system = LocalRAGSystem()
            print("RAG 系统初始化完成")
        finally:
            # 恢复原始工作目录
            os.chdir(original_cwd)
            print(f"已恢复工作目录到: {os.getcwd()}")
    return _rag_system

def set_rag_system(rag_system: LocalRAGSystem):
    """设置 RAG 系统实例"""
    global _rag_system
    _rag_system = rag_system

class QueryRequest(BaseModel):
    """问答请求模型"""
    question: str
    kb_name: Optional[str] = None

class KnowledgeBaseInfo(BaseModel):
    name: str
    document_count: int
    chunk_count: int
    is_current: bool

class KnowledgeBaseCreate(BaseModel):
    """知识库创建请求模型"""
    name: str

class KnowledgeBaseSwitch(BaseModel):
    """切换知识库请求模型"""
    name: str

class KnowledgeBaseError(BaseModel):
    """错误响应模型"""
    status: str
    message: str
    error: Optional[str] = None

class QueryResponse(BaseModel):
    answer: str
    sources: List[Dict[str, Any]]
    kb_name: str
    error: Optional[str] = None

# 根路径已由静态文件服务器处理，指向Vue3前端
# 旧路由已被移除

# 添加路径重定向，确保老的URL路径也能被正确处理
@app.get("/upload-modal", response_class=RedirectResponse)
async def get_upload_modal():
    """将旧的上传模态框路径重定向到新前端"""
    return RedirectResponse(url="/")

@api_router.post("/query")
async def query_rag(query: QueryRequest):
    """处理问答请求"""
    try:
        rag_system = get_rag_system()
        response = rag_system.query(query.question, query.kb_name)
        return response
    except Exception as e:
        return {
            "answer": f"查询出错: {str(e)}",
            "sources": [],
            "kb_name": query.kb_name or "未知",
            "error": str(e)
        }

@api_router.post("/knowledge-bases")
async def create_knowledge_base(kb_data: KnowledgeBaseCreate):
    """创建新知识库"""
    try:
        rag_system = get_rag_system()
        rag_system.create_knowledge_base(kb_data.name)
        return {"status": "success", "message": f"知识库 {kb_data.name} 创建成功"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@api_router.get("/knowledge-bases")
async def list_knowledge_bases():
    """获取所有知识库列表"""
    try:
        rag_system = get_rag_system()
        kb_list = rag_system.list_knowledge_bases()
        return {"status": "success", "data": kb_list}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@api_router.post("/switch-kb")
async def switch_knowledge_base(kb_data: KnowledgeBaseSwitch):
    """切换当前知识库"""
    try:
        rag_system = get_rag_system()
        rag_system.set_current_knowledge_base(kb_data.name)
        return {"status": "success", "message": f"已切换到知识库 {kb_data.name}"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@api_router.delete("/knowledge-bases/{name}")
async def delete_knowledge_base(name: str):
    """删除知识库"""
    try:
        rag_system = get_rag_system()
        rag_system.delete_knowledge_base(name)
        return {"status": "success", "message": f"知识库 {name} 已删除"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@api_router.post("/upload")
async def upload_documents(files: List[UploadFile] = File(...), kb_name: str = Form(...)):
    """上传文档到知识库"""
    if not files:
        return {"status": "error", "message": "未选择文件"}

    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    try:
        # 保存上传的文件到临时目录
        for file in files:
            file_path = os.path.join(temp_dir, file.filename)
            with open(file_path, "wb") as f:
                shutil.copyfileobj(file.file, f)

        # 加载文档到知识库
        rag_system = get_rag_system()
        rag_system.load_documents_to_kb(temp_dir, kb_name)

        return {
            "status": "success",
            "message": f"已上传 {len(files)} 个文件到知识库 {kb_name}"
        }
    except Exception as e:
        return {"status": "error", "message": str(e)}
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir)

@api_router.get("/stats")
async def get_stats():
    """获取系统统计信息"""
    try:
        rag_system = get_rag_system()
        kb_list = rag_system.list_knowledge_bases()

        total_docs = sum(kb["document_count"] for kb in kb_list)
        total_chunks = sum(kb["chunk_count"] for kb in kb_list)

        return {
            "status": "success",
            "data": {
                "kb_count": len(kb_list),
                "total_documents": total_docs,
                "total_chunks": total_chunks,
                "current_kb": next((kb["name"] for kb in kb_list if kb["is_current"]), None)
            }
        }
    except Exception as e:
        return {"status": "error", "message": str(e)}

# 先注册 API 路由
app.include_router(api_router)

# 挂载静态文件
app.mount("/app", StaticFiles(directory=frontend_dist_path, html=True), name="frontend")

# 添加根路径重定向
@app.get("/")
async def redirect_to_app():
    return RedirectResponse(url="/app/", status_code=307)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8002, reload=True)
