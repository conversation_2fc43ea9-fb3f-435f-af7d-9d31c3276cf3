# OneFlower - 本地化 RAG 知识库系统

OneFlower 是一个基于 LangChain v0.3 构建的本地化检索增强生成（RAG）系统，能够从多种文档格式中提取信息，并利用大型语言模型提供准确的问答服务。

## 项目特点

- 🔍 支持多种文档格式（PDF、TXT、Markdown）
- 🧠 使用本地 Ollama 的 bge-m3 作为嵌入模型
- 💬 使用 DeepSeek V3 作为问答模型
- 📊 使用 Chroma 作为向量数据库
- 🚀 基于最新的 LangChain v0.3 API 构建

## 系统架构

```mermaid
graph TD
    A[文档加载] --> B[文本分割]
    B --> C[向量化]
    C --> D[存储到Chroma]
    E[用户问题] --> F[检索相关文档]
    F --> G[生成回答]
    D -.-> F
```

## 组件详解

### 1. 文档处理组件

| 组件 | 版本 | 作用 |
|------|------|------|
| DirectoryLoader | langchain-community>=0.3.0 | 批量加载指定目录下的文档 |
| PyPDFLoader | langchain-community>=0.3.0 | 加载PDF文档 |
| TextLoader | langchain-community>=0.3.0 | 加载TXT文本文档 |
| UnstructuredMarkdownLoader | langchain-community>=0.3.0 | 加载Markdown文档 |
| RecursiveCharacterTextSplitter | langchain-text-splitters>=0.3.0 | 将文档递归分割成适当大小的文本块 |

### 2. 向量化与存储组件

| 组件 | 版本 | 作用 |
|------|------|------|
| OllamaEmbeddings | langchain-ollama>=0.3.0 | 使用本地Ollama的bge-m3模型将文本转换为向量 |
| Chroma | chromadb>=0.4.0 | 向量数据库，存储和检索文本向量 |

### 3. 大语言模型组件

| 组件 | 版本 | 作用 |
|------|------|------|
| ChatDeepSeek | langchain-deepseek>=0.1.0 | 调用DeepSeek API进行问答生成 |

### 4. 检索与生成组件

| 组件 | 版本 | 作用 |
|------|------|------|
| create_retrieval_chain | langchain>=0.3.0 | 创建检索链，连接检索器和文档处理链 |
| create_stuff_documents_chain | langchain>=0.3.0 | 创建文档处理链，将检索到的文档与问题结合 |
| ChatPromptTemplate | langchain>=0.3.0 | 创建提示模板，指导模型生成回答 |
| StrOutputParser | langchain-core>=0.3.0 | 解析模型输出为字符串 |

## 工作流程

1. **初始化**：
   - 初始化嵌入模型（本地Ollama的bge-m3模型）
   - 初始化大语言模型（DeepSeek Chat）

2. **文档加载与处理**：
   - 从指定目录加载PDF、TXT和Markdown文档
   - 使用RecursiveCharacterTextSplitter将文档分割成适当大小的文本块
   - 使用OllamaEmbeddings将文本块转换为向量
   - 将向量存储到Chroma向量数据库中

3. **问答链创建**：
   - 创建自定义提示模板，指导模型如何使用上下文回答问题
   - 创建文档处理链，将检索到的文档与问题结合
   - 创建检索链，连接检索器和文档处理链

4. **查询处理**：
   - 接收用户问题
   - 使用检索链从向量数据库中检索相关文档
   - 将问题和检索到的文档传递给大语言模型
   - 返回生成的回答和相关文档信息

## 依赖包

```
langchain>=0.3.0
langchain-community>=0.3.0
langchain-core>=0.3.0
langchain-text-splitters>=0.3.0
langchain-deepseek>=0.1.0
langchain-ollama>=0.3.0
chromadb>=0.4.0
pypdf>=3.0.0
python-dotenv>=1.0.0
unstructured>=0.10.0
```

## 环境配置

1. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```

2. 配置环境变量：
   创建`.env`文件并添加DeepSeek API密钥：
   ```
   DEEPSEEK_API_KEY=your-api-key
   ```

3. 确保本地Ollama服务运行：
   Ollama服务应在`http://localhost:11434`上运行，并已加载`bge-m3:567m`模型。

## 使用方法

1. 初始化RAG系统：
   ```python
   from local_rag_system import LocalRAGSystem
   
   rag = LocalRAGSystem(
       model_name="deepseek-chat",
       embedding_model="bge-m3:567m"
   )
   ```

2. 加载文档：
   ```python
   rag.load_documents(
       directory="/path/to/your/documents",
       chunk_size=1000,
       chunk_overlap=200
   )
   ```

3. 查询知识库：
   ```python
   answer = rag.query("你的问题")
   print(answer)
   ```

4. 或者直接运行主程序进行交互式问答：
   ```bash
   python local_rag_system.py
   ```

## 系统优势

- **本地化处理**：使用本地Ollama进行向量嵌入，减少API调用成本
- **多格式支持**：支持PDF、TXT和Markdown格式，满足多样化文档需求
- **高质量检索**：使用最大边际相关性(MMR)搜索，提高检索文档的相关性和多样性
- **流式输出**：支持大语言模型的流式输出，提供更好的用户体验
- **持久化存储**：向量数据库持久化存储，避免重复处理文档
